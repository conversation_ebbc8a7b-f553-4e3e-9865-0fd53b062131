#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test script to verify the simplified relationship functionality.
"""
import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from sqlalchemy import Column, Integer, String, ForeignKey
    from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
    from sqlalchemy.orm import DeclarativeBase, relationship, selectinload, joinedload
    from pydantic import BaseModel
    
    from sqlalchemy_crud_plus import CRUDPlus
    
    print("✅ All imports successful!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install required dependencies:")
    print("pip install sqlalchemy pydantic aiosqlite")
    sys.exit(1)


# Simple test models
class Base(DeclarativeBase):
    pass


class Author(Base):
    __tablename__ = 'authors'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    email = Column(String(100), unique=True)
    
    # One-to-many relationship
    books = relationship("Book", back_populates="author")


class Book(Base):
    __tablename__ = 'books'
    
    id = Column(Integer, primary_key=True)
    title = Column(String(200), nullable=False)
    author_id = Column(Integer, ForeignKey('authors.id'))
    
    # Many-to-one relationship
    author = relationship("Author", back_populates="books")


# Pydantic schemas
class AuthorCreate(BaseModel):
    name: str
    email: str


class BookCreate(BaseModel):
    title: str


async def test_relationship_features():
    """Test the simplified relationship functionality."""
    print("\n🧪 Testing simplified relationship features...")
    
    # Create in-memory SQLite database
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)
    async_session = async_sessionmaker(engine, class_=AsyncSession)
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    async with async_session() as session:
        author_crud = CRUDPlus(Author)
        book_crud = CRUDPlus(Book)
        
        try:
            # Test 1: Create test data
            print("  📝 Creating test data...")
            author_data = AuthorCreate(name="Jane Doe", email="<EMAIL>")
            author = await author_crud.create_model(session, author_data)
            
            book_titles = ["Python Guide", "SQLAlchemy Mastery", "Async Programming"]
            for title in book_titles:
                book_data = BookCreate(title=title)
                await book_crud.create_model(session, book_data, author_id=author.id)
            
            await session.commit()
            print(f"  ✅ Created author and {len(book_titles)} books")
            
            # Test 2: Query with options (selectinload)
            print("  🔍 Testing select_with_options with selectinload...")
            authors_with_books = await author_crud.select_with_options(
                session,
                options=[selectinload(Author.books)],
                name="Jane Doe"
            )
            
            if authors_with_books and len(authors_with_books[0].books) == 3:
                print("  ✅ selectinload works correctly")
            else:
                print("  ❌ selectinload failed")
                return False
            
            # Test 3: Query single model with options (joinedload)
            print("  🔍 Testing select_model_with_options with joinedload...")
            author_with_books = await author_crud.select_model_with_options(
                session, author.id,
                options=[selectinload(Author.books)]
            )
            
            if author_with_books and len(author_with_books.books) == 3:
                print("  ✅ select_model_with_options works correctly")
            else:
                print("  ❌ select_model_with_options failed")
                return False
            
            # Test 4: Relationship filtering
            print("  🔍 Testing relationship filtering...")
            jane_books = await book_crud.select_with_options(
                session,
                options=[selectinload(Book.author)],
                author__name="Jane Doe"
            )
            
            if jane_books and len(jane_books) == 3:
                print("  ✅ Relationship filtering works correctly")
                for book in jane_books:
                    print(f"    - '{book.title}' by {book.author.name}")
            else:
                print("  ❌ Relationship filtering failed")
                return False
            
            # Test 5: Query by column with options
            print("  🔍 Testing select_model_by_column_with_options...")
            python_book = await book_crud.select_model_by_column_with_options(
                session,
                options=[joinedload(Book.author)],
                title="Python Guide"
            )
            
            if python_book and python_book.author.name == "Jane Doe":
                print("  ✅ select_model_by_column_with_options works correctly")
            else:
                print("  ❌ select_model_by_column_with_options failed")
                return False
            
            print("  ✅ All relationship tests passed!")
            return True
            
        except Exception as e:
            await session.rollback()
            print(f"  ❌ Test failed with error: {e}")
            import traceback
            traceback.print_exc()
            return False


async def test_method_availability():
    """Test that all new methods are available."""
    print("\n🔍 Testing method availability...")
    
    crud = CRUDPlus(Author)
    
    # Check if new methods exist
    new_methods = [
        'select_with_options',
        'select_model_with_options',
        'select_model_by_column_with_options'
    ]
    
    for method_name in new_methods:
        if hasattr(crud, method_name):
            print(f"  ✅ {method_name} method exists")
        else:
            print(f"  ❌ {method_name} method missing")
            return False
    
    print("  ✅ All new methods are available!")
    return True


async def main():
    """Run all tests."""
    print("🚀 Testing SQLAlchemy CRUD Plus Simplified Relationship Features")
    print("=" * 70)
    
    try:
        # Test method availability
        methods_available = await test_method_availability()
        
        # Test functionality
        features_working = await test_relationship_features()
        
        print("\n" + "=" * 70)
        if methods_available and features_working:
            print("🎉 All tests passed! Simplified relationship functionality is working correctly.")
            print("\n📝 Available features:")
            print("  - select_with_options: Query with SQLAlchemy options")
            print("  - select_model_with_options: Query single model with options")
            print("  - select_model_by_column_with_options: Query by column with options")
            print("  - Relationship filtering: Filter across relationships")
            print("  - All SQLAlchemy loading strategies supported")
            return 0
        else:
            print("❌ Some tests failed. Please check the implementation.")
            return 1
            
    except Exception as e:
        print(f"❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
