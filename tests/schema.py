#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from pydantic import BaseModel


class ModelTest(BaseModel):
    name: str


class ModelTestPks(BaseModel):
    id: int
    name: str
    sex: str


# ==================== Relationship Test Schemas ====================

class UserCreate(BaseModel):
    username: str
    email: str
    is_active: bool = True


class UserProfileCreate(BaseModel):
    bio: str | None = None
    avatar_url: str | None = None


class CategoryCreate(BaseModel):
    name: str
    parent_id: int | None = None


class PostCreate(BaseModel):
    title: str
    content: str
    category_id: int | None = None
    is_published: bool = False


class RoleCreate(BaseModel):
    name: str
    description: str | None = None
