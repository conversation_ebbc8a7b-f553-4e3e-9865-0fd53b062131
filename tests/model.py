#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from datetime import datetime

from sqlalchemy import String, ForeignKey, Table
from sqlalchemy.orm import DeclarativeBase, Mapped, MappedAsDataclass, declared_attr, mapped_column, relationship


class Base(MappedAsDataclass, DeclarativeBase):
    @declared_attr.directive
    def __tablename__(cls) -> str:
        return cls.__name__.lower()


class Ins(Base):
    __tablename__ = 'ins'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, index=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(64))
    del_flag: Mapped[bool] = mapped_column(default=False)
    created_time: Mapped[datetime] = mapped_column(init=False, default_factory=datetime.now)
    updated_time: Mapped[datetime | None] = mapped_column(init=False, onupdate=datetime.now)


class InsPks(Base):
    __tablename__ = 'ins_pks'

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(64))
    sex: Mapped[str] = mapped_column(String(16), primary_key=True, index=True)
    del_flag: Mapped[bool] = mapped_column(default=False)
    created_time: Mapped[datetime] = mapped_column(init=False, default_factory=datetime.now)
    updated_time: Mapped[datetime | None] = mapped_column(init=False, onupdate=datetime.now)


# ==================== Relationship Test Models ====================

# Many-to-many association table
user_role_association = Table(
    'user_roles',
    Base.metadata,
    mapped_column('user_id', ForeignKey('users.id'), primary_key=True),
    mapped_column('role_id', ForeignKey('roles.id'), primary_key=True)
)


class User(Base):
    __tablename__ = 'users'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, autoincrement=True)
    username: Mapped[str] = mapped_column(String(50), unique=True)
    email: Mapped[str] = mapped_column(String(100))
    is_active: Mapped[bool] = mapped_column(default=True)
    created_time: Mapped[datetime] = mapped_column(init=False, default_factory=datetime.now)

    # One-to-one relationship
    profile: Mapped["UserProfile"] = relationship("UserProfile", back_populates="user", uselist=False)

    # One-to-many relationship
    posts: Mapped[list["Post"]] = relationship("Post", back_populates="author")

    # Many-to-many relationship
    roles: Mapped[list["Role"]] = relationship("Role", secondary=user_role_association, back_populates="users")


class UserProfile(Base):
    __tablename__ = 'user_profiles'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, autoincrement=True)
    user_id: Mapped[int] = mapped_column(ForeignKey('users.id'), unique=True)
    bio: Mapped[str | None] = mapped_column(String(500))
    avatar_url: Mapped[str | None] = mapped_column(String(255))

    # One-to-one relationship
    user: Mapped["User"] = relationship("User", back_populates="profile")


class Category(Base):
    __tablename__ = 'categories'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(100))
    parent_id: Mapped[int | None] = mapped_column(ForeignKey('categories.id'))

    # Self-referential relationship
    parent: Mapped["Category | None"] = relationship("Category", remote_side=[id], back_populates="children")
    children: Mapped[list["Category"]] = relationship("Category", back_populates="parent")

    # One-to-many relationship
    posts: Mapped[list["Post"]] = relationship("Post", back_populates="category")


class Post(Base):
    __tablename__ = 'posts'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, autoincrement=True)
    title: Mapped[str] = mapped_column(String(200))
    content: Mapped[str] = mapped_column(String(1000))
    author_id: Mapped[int] = mapped_column(ForeignKey('users.id'))
    category_id: Mapped[int | None] = mapped_column(ForeignKey('categories.id'))
    is_published: Mapped[bool] = mapped_column(default=False)
    created_time: Mapped[datetime] = mapped_column(init=False, default_factory=datetime.now)

    # Many-to-one relationships
    author: Mapped["User"] = relationship("User", back_populates="posts")
    category: Mapped["Category | None"] = relationship("Category", back_populates="posts")


class Role(Base):
    __tablename__ = 'roles'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(50), unique=True)
    description: Mapped[str | None] = mapped_column(String(200))

    # Many-to-many relationship
    users: Mapped[list["User"]] = relationship("User", secondary=user_role_association, back_populates="roles")
