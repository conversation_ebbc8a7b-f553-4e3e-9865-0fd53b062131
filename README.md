# sqlalchemy-crud-plus

基于 SQLAlChemy 2.0 的异步 CRUD 操作

## Download

```shell
pip install sqlalchemy-crud-plus
```

## 特性

- **异步支持**: 基于 SQLAlchemy 2.0+ 的异步操作
- **类型安全**: 完整的类型注解支持
- **灵活过滤**: 支持复杂的查询条件和操作符
- **批量操作**: 高效的批量创建、更新和删除
- **事务管理**: 灵活的事务控制
- **错误处理**: 完善的异常处理机制
- **🆕 关系支持**: 全面的 SQLAlchemy 关系表支持
  - 一对一、一对多、多对一、多对多关系
  - 自关联关系支持
  - 智能加载策略 (selectinload, joinedload, subqueryload)
  - 跨关系查询和过滤
  - 嵌套关系加载

## 快速开始

### 基本 CRUD 操作

```python
from sqlalchemy_crud_plus import CRUDPlus

# 创建 CRUD 实例
user_crud = CRUDPlus(User)

# 创建记录
user = await user_crud.create_model(session, user_data)

# 查询记录
user = await user_crud.select_model(session, user_id)
users = await user_crud.select_models(session, is_active=True)

# 更新记录
await user_crud.update_model(session, user_id, update_data)

# 删除记录
await user_crud.delete_model(session, user_id)
```

### 🆕 关系操作

```python
# 查询用户及其文章
user_with_posts = await user_crud.select_model_with_relationships(
    session, user_id, relationships="posts"
)

# 查询文章及其作者和标签
post_with_details = await post_crud.select_model_with_relationships(
    session, post_id,
    relationships=["author", "tags"]
)

# 嵌套关系查询
post_full = await post_crud.select_model_with_relationships(
    session, post_id,
    relationships={
        "author": {"relationships": "profile"},
        "comments": {"relationships": "author"}
    }
)

# 跨关系过滤
alice_posts = await post_crud.select_models_with_relationships(
    session,
    relationships="author",
    author__username="alice"
)

# 创建带关系的记录
user = await user_crud.create_model_with_relationships(
    session,
    user_data,
    relationships_data={"profile": profile_instance}
)
```

## 文档

[SQLAlchemy CRUD Plus](https://fastapi-practices.github.io/sqlalchemy-crud-plus)

## 互动

[Discord](https://wu-clan.github.io/homepage/)

## 赞助

如果此项目能够帮助到你，你可以赞助作者一些咖啡豆表示鼓励：[:coffee: Sponsor :coffee:](https://wu-clan.github.io/sponsor/)
