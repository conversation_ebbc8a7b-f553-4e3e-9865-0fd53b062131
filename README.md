# sqlalchemy-crud-plus

基于 SQLAlChemy 2.0 的异步 CRUD 操作

## Download

```shell
pip install sqlalchemy-crud-plus
```

## 特性

- **异步支持**: 基于 SQLAlchemy 2.0+ 的异步操作
- **类型安全**: 完整的类型注解支持
- **灵活过滤**: 支持复杂的查询条件和操作符
- **批量操作**: 高效的批量创建、更新和删除
- **事务管理**: 灵活的事务控制
- **错误处理**: 完善的异常处理机制
- **🆕 关系支持**: SQLAlchemy 关系表和 options 支持
  - 支持所有 SQLAlchemy 关系类型
  - 完整的 options 支持 (selectinload, joinedload, subqueryload 等)
  - 跨关系查询和过滤

## 快速开始

### 基本 CRUD 操作

```python
from sqlalchemy_crud_plus import CRUDPlus

# 创建 CRUD 实例
user_crud = CRUDPlus(User)

# 创建记录
user = await user_crud.create_model(session, user_data)

# 查询记录
user = await user_crud.select_model(session, user_id)
users = await user_crud.select_models(session, is_active=True)

# 更新记录
await user_crud.update_model(session, user_id, update_data)

# 删除记录
await user_crud.delete_model(session, user_id)
```

### 🆕 关系和 Options 支持

```python
from sqlalchemy.orm import selectinload, joinedload

# 使用 SQLAlchemy options 查询关系
users_with_posts = await user_crud.select_with_options(
    session,
    options=[selectinload(User.posts)],
    is_active=True
)

# 查询单个记录并加载关系
user_with_profile = await user_crud.select_model_with_options(
    session, user_id,
    options=[joinedload(User.profile)]
)

# 跨关系过滤
alice_posts = await post_crud.select_with_options(
    session,
    options=[selectinload(Post.author)],
    author__username="alice"
)

# 复杂关系过滤
published_posts = await post_crud.select_with_options(
    session,
    options=[selectinload(Post.author), selectinload(Post.category)],
    author__is_active=True,
    category__name="Technology",
    is_published=True
)
```

## 文档

[SQLAlchemy CRUD Plus](https://fastapi-practices.github.io/sqlalchemy-crud-plus)

## 互动

[Discord](https://wu-clan.github.io/homepage/)

## 赞助

如果此项目能够帮助到你，你可以赞助作者一些咖啡豆表示鼓励：[:coffee: Sponsor :coffee:](https://wu-clan.github.io/sponsor/)
