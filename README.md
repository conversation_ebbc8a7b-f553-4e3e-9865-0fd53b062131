# sqlalchemy-crud-plus

基于 SQLAlChemy 2.0 的异步 CRUD 操作

## Download

```shell
pip install sqlalchemy-crud-plus
```

## 特性

- **异步支持**: 基于 SQLAlchemy 2.0+ 的异步操作
- **类型安全**: 完整的类型注解支持
- **灵活过滤**: 支持复杂的查询条件和操作符
- **批量操作**: 高效的批量创建、更新和删除
- **事务管理**: 灵活的事务控制
- **错误处理**: 完善的异常处理机制
- **🆕 高级查询支持**: 全面的 SQLAlchemy 关联查询支持
  - 支持所有 SQLAlchemy 关系类型和加载策略
  - 完整的 JOIN 支持 (内连接、外连接、手动连接)
  - 高级 options 支持 (selectinload, joinedload, subqueryload, defer 等)
  - 跨关系查询和过滤
  - 嵌套关系加载

## 快速开始

### 基本 CRUD 操作

```python
from sqlalchemy_crud_plus import CRUDPlus

# 创建 CRUD 实例
user_crud = CRUDPlus(User)

# 创建记录
user = await user_crud.create_model(session, user_data)

# 查询记录
user = await user_crud.select_model(session, user_id)
users = await user_crud.select_models(session, is_active=True)

# 更新记录
await user_crud.update_model(session, user_id, update_data)

# 删除记录
await user_crud.delete_model(session, user_id)
```

### 🆕 高级查询支持

```python
from sqlalchemy.orm import selectinload, joinedload, defer

# 1. 增强的现有方法 - 支持 options 和 joins
users_with_posts = await user_crud.select_models(
    session,
    options=[selectinload(User.posts)],
    joins=[{'target': 'profile', 'type': 'left'}],
    is_active=True
)

# 2. 新的高级查询方法
posts = await post_crud.query(
    session,
    joins=[
        {'target': 'author', 'type': 'inner'},
        {'target': 'category', 'type': 'left'}
    ],
    options=[selectinload(Post.author), defer(Post.content)],
    sort_columns=["created_time", "title"],
    sort_orders=["desc", "asc"],
    limit=10,
    offset=0,
    author__is_active=True,
    category__name="Technology"
)

# 3. 便捷的连接查询
posts = await post_crud.query_with_joins(
    session,
    inner_joins=["author"],
    left_joins=["category"],
    options=[selectinload(Post.author.profile)],
    is_published=True
)

# 4. 自动关系加载
users = await user_crud.query_with_relationships(
    session,
    relationships={
        "posts": {
            "strategy": "selectinload",
            "relationships": "category"
        },
        "profile": {
            "strategy": "joinedload"
        }
    },
    is_active=True
)

# 5. 跨关系过滤和复杂查询
posts = await post_crud.query_one(
    session,
    joins=[{'target': 'author', 'type': 'inner'}],
    options=[joinedload(Post.author), selectinload(Post.comments)],
    author__username="alice",
    author__is_active=True,
    title__icontains="python"
)
```

## 文档

[SQLAlchemy CRUD Plus](https://fastapi-practices.github.io/sqlalchemy-crud-plus)

## 互动

[Discord](https://wu-clan.github.io/homepage/)

## 赞助

如果此项目能够帮助到你，你可以赞助作者一些咖啡豆表示鼓励：[:coffee: Sponsor :coffee:](https://wu-clan.github.io/sponsor/)
